#include "uart.h"
uint8_t uart_rx_dma_buffer[128] = {0};  // UART接收DMA缓冲区
uint8_t uart_dma_buffer[128] = {0};     // UART数据处理缓冲区
uint8_t uart_flag = 0;                  // UART数据接收标志
uint8_t uart_ringbuffer_data[128];      // 环形缓冲区数据存储
struct rt_ringbuffer uart_ringbuffer;   // 环形缓冲区结构体
// 配置说明：首先配置RX和TX引脚，然后在CubeMX中将串口设置为异步模式
// 在NVIC中打开对应的中断，配置RX使用DMA模式为circular，Data Width为Word
// 移植RT-Thread的ringbuffer库，在mydefine.h中包含ringbuffer.h头文件
// 然后定义uint8_t uart_ringbuffer_data[128]和struct rt_ringbuffer uart_ringbuffer变量
// 在main.c中初始化Uart_Init()函数，注意可能需要开启use MicroLIB



int my_printf(UART_HandleTypeDef*huart, const char *format, ...)
{
	char buffer[512];
	va_list arg;
	int len;
	// 初始化可变参数列表
	va_start(arg, format);
	len = vsnprintf(buffer, sizeof(buffer), format, arg);
	va_end(arg);
	HAL_UART_Transmit(huart, (uint8_t *)buffer, (uint16_t)len, 0xFF);
	return len;
} // 自定义printf函数，通过UART输出

void Uart_Inti()
{
	 rt_ringbuffer_init(&uart_ringbuffer, uart_ringbuffer_data, sizeof(uart_ringbuffer_data));  // 初始化环形缓冲区
    HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_dma_buffer, sizeof(uart_rx_dma_buffer));      // 启动UART DMA接收
  __HAL_DMA_DISABLE_IT(&hdma_usart1_rx, DMA_IT_HT);  // 禁用DMA半传输中断
}

/**
 * @brief UART DMA接收完成或空闲事件回调函数
 * @param huart UART句柄
 * @param Size 指示此事件中当前DMA已经成功接收了多少字节的数据
 * @retval None
 */
void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size)
{
    // 1. 确认是目标串口 (USART1)
    if (huart->Instance == USART1)
    {
        // 2. 立即停止当前的DMA传输 (避免正在接收中)
        //    因为此中断意味着发送方已经停止或者DMA缓冲等待超时
        HAL_UART_DMAStop(huart);

        // 3. 将接收到的数据写入环形缓冲区
        rt_ringbuffer_put(&uart_ringbuffer, uart_rx_dma_buffer, Size);
        // 注意：这里使用了Size，只处理实际接收到的数据

        // 4. 设置"数据通知标志"，主循环中数据处理任务
        uart_flag = 1;

        // 5. 清空DMA接收缓冲区为下次接收做准备
        //    虽然memcpy只处理Size个数据，但清空整个缓冲区更安全
        memset(uart_rx_dma_buffer, 0, sizeof(uart_rx_dma_buffer));

        // 6. **关键步骤：重新启动DMA接收任务**
        //    如果不再次调用，串口只能接收一次
        HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_dma_buffer, sizeof(uart_rx_dma_buffer));

        // 7. 如果之前关闭了半传输中断，这里需要再次关闭 (可选需要)
        __HAL_DMA_DISABLE_IT(&hdma_usart1_rx, DMA_IT_HT);
    }
}

void uart_task(void)
{
	// 如果标志位为0，说明没有数据需要处理，直接返回
	if (uart_flag == 0)
		return;

    uart_flag = 0;  // 清除标志位
/*
	uint16_t length;
	length = rt_ringbuffer_data_len(&uart_ringbuffer);
  if(length>0)
	{
		rt_ringbuffer_get(&uart_ringbuffer, uart_dma_buffer, length);
	if(strncmp((char*)uart_dma_buffer,"light",5)==0)
	{
	   ucLed[1]=1;
		my_printf(&huart1,"Led1 light\r\n");
	}
			memset(uart_dma_buffer, 0, sizeof(uart_dma_buffer));
  }
   */
	uint16_t length;
	length = rt_ringbuffer_data_len(&uart_ringbuffer);  // 获取环形缓冲区数据长度
	if (length > 0)
	{
		rt_ringbuffer_get(&uart_ringbuffer, uart_dma_buffer, length);  // 从环形缓冲区读取数据
   //   parse_uart_command(uart_dma_buffer, length);  // 解析UART命令
		memset(uart_dma_buffer, 0, sizeof(uart_dma_buffer));  // 清空缓冲区
  }
}


