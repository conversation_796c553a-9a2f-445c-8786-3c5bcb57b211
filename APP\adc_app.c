#include "adc_app.h"
#include "math.h"
#include "string.h" // 用于memset函数
 //DMA配置时注意：
 //DMA循环模式   Data Width:Word   Continuous Conversion Mode:Enabled  DMA Continuous Request:Enabled
// --- 初始化 (通常在 main 函数或其他初始化函数中调用一次) ---
/*
#define ADC_DMA_BUFFER_SIZE 32 // DMA��������С�����Ը�����Ҫ����
uint32_t adc_dma_buffer[ADC_DMA_BUFFER_SIZE]; // DMA Ŀ�껺����
__IO uint32_t adc_val;  // ���ڴ洢������ƽ�� ADC ֵ
__IO float voltage; // ���ڴ洢�����ĵ�ѹֵ

void adc_dma_init(void)
{
    // ���� ADC ��ʹ�� DMA ����
    // hadc1: ADC ���
    // (uint32_t*)adc_dma_buffer: DMA Ŀ�껺������ַ (HAL��ͨ����Ҫuint32_t*)
    // ADC_DMA_BUFFER_SIZE: ���δ���������� (��������С)

    HAL_ADC_Start_DMA(&hadc1, (uint32_t*)adc_dma_buffer, ADC_DMA_BUFFER_SIZE);

}

// --- �������� (����ѭ����ʱ���ص��ж��ڵ���) ---
void adc_task(void)
{
    uint32_t adc_sum = 0;

    // 1. ���� DMA �����������в���ֵ���ܺ�
    //    ע�⣺����ֱ�Ӷ�ȡ�����������ܰ�����ͬʱ�̵Ĳ���ֵ
    for(uint16_t i = 0; i < ADC_DMA_BUFFER_SIZE; i++)
    {
        adc_sum += adc_dma_buffer[i];
    }

    // 2. ����ƽ�� ADC ֵ
    adc_val = adc_sum / ADC_DMA_BUFFER_SIZE;

    // 3. (��ѡ) ��ƽ������ֵת��Ϊʵ�ʵ�ѹֵ
    voltage = ((float)adc_val * 3.3f) / 4096.0f; // ����12λ�ֱ���, 3.3V�ο���ѹ

    // 4. ʹ�ü������ƽ��ֵ (adc_val �� voltage)
    my_printf(&huart1, "Average ADC: %lu, Voltage: %.2fV\n", adc_val, voltage);
}

}*/

//��ʱ������+DMA+�鴦��
//
#define SINE_SAMPLES 100    // һ�������ڵĲ�������
#define DAC_MAX_VALUE 4095 // 12 λ DAC ���������ֵ (2^12 - 1)

uint16_t SineWave[SINE_SAMPLES]; // �洢���Ҳ����ݵ�����

// --- �������Ҳ����ݵĺ��� ---
/**
 * @brief �������Ҳ����ұ�
 * @param buffer: �洢�������ݵĻ�����ָ��
 * @param samples: һ�������ڵĲ�������
 * @param amplitude: ���Ҳ��ķ�ֵ���� (���������ֵ)
 * @param phase_shift: ��λƫ�� (����)
 * @retval None
 */

#define BUFFER_SIZE 1000

extern DMA_HandleTypeDef hdma_adc1;
extern ADC_HandleTypeDef hadc1;
extern TIM_HandleTypeDef htim3;

uint32_t dac_val_buffer[BUFFER_SIZE / 2];
__IO uint32_t adc_val_buffer[BUFFER_SIZE];

__IO uint8_t AdcConvEnd = 0;


void adc_tim_dma_init(void)
{
		HAL_TIM_Base_Start(&htim3);
    HAL_ADC_Start_DMA(&hadc1, (uint32_t *)adc_val_buffer, BUFFER_SIZE);
    __HAL_DMA_DISABLE_IT(&hdma_adc1, DMA_IT_HT);
}

void HAL_ADC_ConvCpltCallback(ADC_HandleTypeDef* hadc)
{
    UNUSED(hadc);
    if(hadc == &hadc1)
    {
        HAL_ADC_Stop_DMA(hadc);
        AdcConvEnd = 1;
    }
}

static float current_voltage = 2.5f; // 当前电压值，初始值2.5V

// 获取当前电压值的函数
float get_voltage_value(void)
{
    // 模拟电压变化，实际使用时可以从ADC读取真实值
    static uint8_t direction = 1;

    if(direction) {
        current_voltage += 0.05f;
        if(current_voltage >= 3.3f) direction = 0;
    } else {
        current_voltage -= 0.05f;
        if(current_voltage <= 0.5f) direction = 1;
    }

    return current_voltage;
	}
void adc_task(void)
{
    // 一次采样转换 3 + 12.5 =15.5个ADC时钟周期
    // 一次采样转换 15.5 / 22.5 = 0.68us
    // 1000次采样转换需要 1000 * 10 = 10000us = 10ms
//    while(!AdcConvEnd);
    if(AdcConvEnd)
    {
        for(uint16_t i = 0; i < BUFFER_SIZE / 2; i++)
        {
            dac_val_buffer[i] = adc_val_buffer[i * 2 + 1];
        }
        for(uint16_t i = 0; i < BUFFER_SIZE / 2; i++)
        {
            //my_printf(&huart1, "{dac}%d\r\n", (int)dac_val_buffer[i]);
        }
        memset(dac_val_buffer, 0, sizeof(uint32_t) * (BUFFER_SIZE / 2));
        HAL_ADC_Start_DMA(&hadc1, (uint32_t *)adc_val_buffer, BUFFER_SIZE);
        __HAL_DMA_DISABLE_IT(&hdma_adc1, DMA_IT_HT);
        AdcConvEnd = 0;
    }
}
